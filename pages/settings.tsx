// pages/settings.tsx
import React, { useState, useEffect } from 'react';
import SidebarLayout from '../components/SidebarLayoutSimple';
import { Save, Plus, Trash2, Bot, Key, Twitter, User, Bell, Shield, Zap, Edit3, Crown } from 'lucide-react';
import { useUser } from '../contexts/UserContext';
import { supabase } from '../lib/supabase';
import type { ReactElement } from 'react';
import type { NextPageWithLayout } from './_app';

const SettingsPage: NextPageWithLayout = () => {
  const { user, profile, updateProfile } = useUser();
  const [activeTab, setActiveTab] = useState('agent-e');
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);

  // Agent E Settings
  const [xApiKey, setXApiKey] = useState('');
  const [xApiSecret, setXApiSecret] = useState('');
  const [projectName, setProjectName] = useState('My AI Project');
  const [projectDescription, setProjectDescription] = useState('');
  const [targetAudience, setTargetAudience] = useState('');
  const [brandVoice, setBrandVoice] = useState('professional');
  const [customPrompts, setCustomPrompts] = useState([
    { id: 1, name: 'Product Launch', prompt: 'Create engaging content for product launches with excitement and clear benefits' },
    { id: 2, name: 'Educational', prompt: 'Write informative content that teaches and provides value to the audience' }
  ]);

  // Profile Settings
  const [fullName, setFullName] = useState('');
  const [email, setEmail] = useState('');

  // Load settings from Supabase
  useEffect(() => {
    if (user) {
      loadSettings();
      setFullName(profile?.full_name || user.user_metadata?.full_name || '');
      setEmail(user.email || '');
    }
  }, [user, profile]);

  const loadSettings = async () => {
    if (!user) return;

    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('agent_e_settings')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('Error loading settings:', error);
        return;
      }

      if (data) {
        setProjectName(data.project_name || 'My AI Project');
        setProjectDescription(data.project_description || '');
        setTargetAudience(data.target_audience || '');
        setBrandVoice(data.brand_voice || 'professional');
        setCustomPrompts(data.custom_prompts || []);
        setXApiKey(data.x_api_key || '');
        setXApiSecret(data.x_api_secret || '');
      }
    } catch (error) {
      console.error('Error loading settings:', error);
    } finally {
      setLoading(false);
    }
  };

  const saveAgentESettings = async () => {
    if (!user) return;

    setSaving(true);
    try {
      const { error } = await supabase
        .from('agent_e_settings')
        .upsert({
          user_id: user.id,
          project_name: projectName,
          project_description: projectDescription,
          target_audience: targetAudience,
          brand_voice: brandVoice,
          custom_prompts: customPrompts,
          x_api_key: xApiKey,
          x_api_secret: xApiSecret
        });

      if (error) {
        console.error('Error saving settings:', error);
        alert('Error saving settings. Please try again.');
      } else {
        alert('Settings saved successfully!');
      }
    } catch (error) {
      console.error('Error saving settings:', error);
      alert('Error saving settings. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const saveProfileSettings = async () => {
    if (!user || !profile) return;

    setSaving(true);
    try {
      const { error } = await updateProfile({
        full_name: fullName
      });

      if (error) {
        console.error('Error updating profile:', error);
        alert('Error updating profile. Please try again.');
      } else {
        alert('Profile updated successfully!');
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      alert('Error updating profile. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const colors = {
    primary: '#FF6B35',
    primaryLight: '#FF8A65',
    surface: '#FFFFFF',
    background: '#FFF8F3',
    text: {
      primary: '#2D1B14',
      secondary: '#5D4037',
      tertiary: '#8D6E63'
    },
    border: '#F5E6D3'
  };

  const tabs = [
    { id: 'agent-e', label: 'Agent E', icon: Bot },
    { id: 'account', label: 'Account', icon: User },
    { id: 'integrations', label: 'Integrations', icon: Zap },
    { id: 'notifications', label: 'Notifications', icon: Bell },
    { id: 'security', label: 'Security', icon: Shield }
  ];

  const handleSavePrompt = (id: number, newPrompt: string) => {
    setCustomPrompts(prev => prev.map(p => p.id === id ? { ...p, prompt: newPrompt } : p));
  };

  const addNewPrompt = () => {
    const newId = Math.max(...customPrompts.map(p => p.id)) + 1;
    setCustomPrompts(prev => [...prev, { id: newId, name: 'New Prompt', prompt: '' }]);
  };

  const deletePrompt = (id: number) => {
    setCustomPrompts(prev => prev.filter(p => p.id !== id));
  };

  return (
    <div style={{
      padding: '40px 60px',
      height: '100vh',
      overflow: 'auto',
      background: colors.background
    }}>
      {/* Header */}
      <div style={{
        marginBottom: '40px'
      }}>
        <h1 style={{
          color: colors.text.primary,
          margin: 0,
          fontSize: '32px',
          fontWeight: '300',
          letterSpacing: '-1px',
          marginBottom: '8px',
          fontFamily: 'Georgia, serif'
        }}>
          Settings
        </h1>
        <p style={{
          color: colors.text.secondary,
          margin: 0,
          fontSize: '16px'
        }}>
          Configure Agent E and manage your account preferences
        </p>
      </div>

      {/* Tab Navigation */}
      <div style={{
        display: 'flex',
        gap: '8px',
        marginBottom: '40px',
        borderBottom: `1px solid ${colors.border}`,
        paddingBottom: '0'
      }}>
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              padding: '12px 20px',
              background: activeTab === tab.id ? colors.surface : 'transparent',
              border: activeTab === tab.id ? `1px solid ${colors.border}` : '1px solid transparent',
              borderBottom: activeTab === tab.id ? `1px solid ${colors.surface}` : '1px solid transparent',
              borderRadius: '8px 8px 0 0',
              fontSize: '14px',
              fontWeight: '500',
              color: activeTab === tab.id ? colors.text.primary : colors.text.secondary,
              cursor: 'pointer',
              transition: 'all 0.2s ease',
              marginBottom: '-1px'
            }}
          >
            <tab.icon size={16} />
            {tab.label}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div style={{
        background: colors.surface,
        borderRadius: '12px',
        padding: '32px',
        border: `1px solid ${colors.border}`,
        minHeight: '500px'
      }}>
        {activeTab === 'agent-e' && (
          <div>
            <h2 style={{
              color: colors.text.primary,
              fontSize: '24px',
              fontWeight: '600',
              marginBottom: '24px',
              display: 'flex',
              alignItems: 'center',
              gap: '12px'
            }}>
              <Bot size={24} color={colors.primary} />
              Agent E Configuration
            </h2>

            {/* Project Information */}
            <div style={{ marginBottom: '32px' }}>
              <h3 style={{
                color: colors.text.primary,
                fontSize: '18px',
                fontWeight: '600',
                marginBottom: '16px'
              }}>
                Project Information
              </h3>

              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px', marginBottom: '20px' }}>
                <div>
                  <label style={{
                    display: 'block',
                    color: colors.text.secondary,
                    fontSize: '14px',
                    fontWeight: '500',
                    marginBottom: '8px'
                  }}>
                    Project Name
                  </label>
                  <input
                    type="text"
                    value={projectName}
                    onChange={(e) => setProjectName(e.target.value)}
                    style={{
                      width: '100%',
                      padding: '12px 16px',
                      border: `1px solid ${colors.border}`,
                      borderRadius: '8px',
                      fontSize: '14px',
                      background: colors.background,
                      outline: 'none'
                    }}
                  />
                </div>

                <div>
                  <label style={{
                    display: 'block',
                    color: colors.text.secondary,
                    fontSize: '14px',
                    fontWeight: '500',
                    marginBottom: '8px'
                  }}>
                    Brand Voice
                  </label>
                  <select
                    value={brandVoice}
                    onChange={(e) => setBrandVoice(e.target.value)}
                    style={{
                      width: '100%',
                      padding: '12px 16px',
                      border: `1px solid ${colors.border}`,
                      borderRadius: '8px',
                      fontSize: '14px',
                      background: colors.background,
                      outline: 'none'
                    }}
                  >
                    <option value="professional">Professional</option>
                    <option value="casual">Casual & Friendly</option>
                    <option value="technical">Technical</option>
                    <option value="creative">Creative & Fun</option>
                    <option value="authoritative">Authoritative</option>
                  </select>
                </div>
              </div>

              <div style={{ marginBottom: '20px' }}>
                <label style={{
                  display: 'block',
                  color: colors.text.secondary,
                  fontSize: '14px',
                  fontWeight: '500',
                  marginBottom: '8px'
                }}>
                  Project Description
                </label>
                <textarea
                  value={projectDescription}
                  onChange={(e) => setProjectDescription(e.target.value)}
                  placeholder="Describe your project, product, or service so Agent E can create relevant content..."
                  style={{
                    width: '100%',
                    padding: '12px 16px',
                    border: `1px solid ${colors.border}`,
                    borderRadius: '8px',
                    fontSize: '14px',
                    background: colors.background,
                    outline: 'none',
                    minHeight: '100px',
                    resize: 'vertical'
                  }}
                />
              </div>

              <div>
                <label style={{
                  display: 'block',
                  color: colors.text.secondary,
                  fontSize: '14px',
                  fontWeight: '500',
                  marginBottom: '8px'
                }}>
                  Target Audience
                </label>
                <input
                  type="text"
                  value={targetAudience}
                  onChange={(e) => setTargetAudience(e.target.value)}
                  placeholder="e.g., Tech entrepreneurs, Small business owners, Developers..."
                  style={{
                    width: '100%',
                    padding: '12px 16px',
                    border: `1px solid ${colors.border}`,
                    borderRadius: '8px',
                    fontSize: '14px',
                    background: colors.background,
                    outline: 'none'
                  }}
                />
              </div>
            </div>

            {/* Custom Prompts */}
            <div style={{ marginBottom: '32px' }}>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginBottom: '16px'
              }}>
                <h3 style={{
                  color: colors.text.primary,
                  fontSize: '18px',
                  fontWeight: '600',
                  margin: 0
                }}>
                  Custom Prompts
                </h3>
                <button
                  onClick={addNewPrompt}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '6px',
                    padding: '8px 16px',
                    background: `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)`,
                    color: 'white',
                    border: 'none',
                    borderRadius: '8px',
                    fontSize: '14px',
                    fontWeight: '500',
                    cursor: 'pointer'
                  }}
                >
                  <Plus size={16} />
                  Add Prompt
                </button>
              </div>

              {customPrompts.map((prompt) => (
                <div key={prompt.id} style={{
                  border: `1px solid ${colors.border}`,
                  borderRadius: '8px',
                  padding: '16px',
                  marginBottom: '12px',
                  background: colors.background
                }}>
                  <div style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    marginBottom: '12px'
                  }}>
                    <input
                      type="text"
                      value={prompt.name}
                      onChange={(e) => {
                        setCustomPrompts(prev => prev.map(p =>
                          p.id === prompt.id ? { ...p, name: e.target.value } : p
                        ));
                      }}
                      style={{
                        background: 'transparent',
                        border: 'none',
                        fontSize: '16px',
                        fontWeight: '600',
                        color: colors.text.primary,
                        outline: 'none',
                        flex: 1
                      }}
                    />
                    <button
                      onClick={() => deletePrompt(prompt.id)}
                      style={{
                        background: 'none',
                        border: 'none',
                        color: colors.text.tertiary,
                        cursor: 'pointer',
                        padding: '4px'
                      }}
                    >
                      <Trash2 size={16} />
                    </button>
                  </div>
                  <textarea
                    value={prompt.prompt}
                    onChange={(e) => handleSavePrompt(prompt.id, e.target.value)}
                    placeholder="Enter your custom prompt for Agent E..."
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: `1px solid ${colors.border}`,
                      borderRadius: '6px',
                      fontSize: '14px',
                      background: colors.surface,
                      outline: 'none',
                      minHeight: '80px',
                      resize: 'vertical'
                    }}
                  />
                </div>
              ))}
            </div>

            {/* Save Button */}
            <button
              onClick={saveAgentESettings}
              disabled={saving || !user}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                padding: '12px 24px',
                background: saving || !user ? colors.text.tertiary : `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)`,
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                fontSize: '14px',
                fontWeight: '600',
                cursor: saving || !user ? 'not-allowed' : 'pointer',
                boxShadow: saving || !user ? 'none' : `0 4px 12px ${colors.primary}30`,
                opacity: saving || !user ? 0.6 : 1
              }}
            >
              <Save size={16} />
              {saving ? 'Saving...' : 'Save Agent E Settings'}
            </button>
          </div>
        )}

        {activeTab === 'integrations' && (
          <div>
            <h2 style={{
              color: colors.text.primary,
              fontSize: '24px',
              fontWeight: '600',
              marginBottom: '24px',
              display: 'flex',
              alignItems: 'center',
              gap: '12px'
            }}>
              <Zap size={24} color={colors.primary} />
              Integrations
            </h2>

            {/* X/Twitter API */}
            <div style={{
              border: `1px solid ${colors.border}`,
              borderRadius: '12px',
              padding: '24px',
              marginBottom: '24px',
              background: colors.background
            }}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '12px',
                marginBottom: '16px'
              }}>
                <Twitter size={24} color={colors.primary} />
                <h3 style={{
                  color: colors.text.primary,
                  fontSize: '18px',
                  fontWeight: '600',
                  margin: 0
                }}>
                  X (Twitter) API
                </h3>
              </div>

              <p style={{
                color: colors.text.secondary,
                fontSize: '14px',
                marginBottom: '20px'
              }}>
                Connect your X API credentials to enable Agent E to post and schedule content automatically.
              </p>

              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
                <div>
                  <label style={{
                    display: 'block',
                    color: colors.text.secondary,
                    fontSize: '14px',
                    fontWeight: '500',
                    marginBottom: '8px'
                  }}>
                    API Key
                  </label>
                  <input
                    type="password"
                    value={xApiKey}
                    onChange={(e) => setXApiKey(e.target.value)}
                    placeholder="Enter your X API key..."
                    style={{
                      width: '100%',
                      padding: '12px 16px',
                      border: `1px solid ${colors.border}`,
                      borderRadius: '8px',
                      fontSize: '14px',
                      background: colors.surface,
                      outline: 'none'
                    }}
                  />
                </div>

                <div>
                  <label style={{
                    display: 'block',
                    color: colors.text.secondary,
                    fontSize: '14px',
                    fontWeight: '500',
                    marginBottom: '8px'
                  }}>
                    API Secret
                  </label>
                  <input
                    type="password"
                    value={xApiSecret}
                    onChange={(e) => setXApiSecret(e.target.value)}
                    placeholder="Enter your X API secret..."
                    style={{
                      width: '100%',
                      padding: '12px 16px',
                      border: `1px solid ${colors.border}`,
                      borderRadius: '8px',
                      fontSize: '14px',
                      background: colors.surface,
                      outline: 'none'
                    }}
                  />
                </div>
              </div>

              <button
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  padding: '10px 20px',
                  background: colors.surface,
                  color: colors.text.primary,
                  border: `1px solid ${colors.border}`,
                  borderRadius: '8px',
                  fontSize: '14px',
                  fontWeight: '500',
                  cursor: 'pointer',
                  marginTop: '16px'
                }}
              >
                <Key size={16} />
                Test Connection
              </button>
            </div>
          </div>
        )}

        {activeTab === 'account' && (
          <div>
            <h2 style={{
              color: colors.text.primary,
              fontSize: '24px',
              fontWeight: '600',
              marginBottom: '24px',
              display: 'flex',
              alignItems: 'center',
              gap: '12px'
            }}>
              <User size={24} color={colors.primary} />
              Account Settings
            </h2>

            {/* Profile Information */}
            <div style={{ marginBottom: '32px' }}>
              <h3 style={{
                color: colors.text.primary,
                fontSize: '18px',
                fontWeight: '600',
                marginBottom: '16px'
              }}>
                Profile Information
              </h3>

              <div style={{ display: 'grid', gap: '16px', maxWidth: '400px' }}>
                <div>
                  <label style={{
                    display: 'block',
                    color: colors.text.primary,
                    fontSize: '14px',
                    fontWeight: '600',
                    marginBottom: '6px'
                  }}>
                    Full Name
                  </label>
                  <input
                    type="text"
                    value={fullName}
                    onChange={(e) => setFullName(e.target.value)}
                    style={{
                      width: '100%',
                      padding: '12px 16px',
                      border: `1px solid ${colors.border}`,
                      borderRadius: '8px',
                      fontSize: '14px',
                      background: colors.surface,
                      color: colors.text.primary
                    }}
                  />
                </div>

                <div>
                  <label style={{
                    display: 'block',
                    color: colors.text.primary,
                    fontSize: '14px',
                    fontWeight: '600',
                    marginBottom: '6px'
                  }}>
                    Email Address
                  </label>
                  <input
                    type="email"
                    value={email}
                    disabled
                    style={{
                      width: '100%',
                      padding: '12px 16px',
                      border: `1px solid ${colors.border}`,
                      borderRadius: '8px',
                      fontSize: '14px',
                      background: '#F9F9F9',
                      color: colors.text.secondary,
                      cursor: 'not-allowed'
                    }}
                  />
                </div>
              </div>
            </div>

            {/* Subscription Information */}
            <div style={{ marginBottom: '32px' }}>
              <h3 style={{
                color: colors.text.primary,
                fontSize: '18px',
                fontWeight: '600',
                marginBottom: '16px'
              }}>
                Subscription
              </h3>

              <div style={{
                background: `linear-gradient(135deg, ${colors.primary}15 0%, ${colors.primaryLight}15 100%)`,
                border: `1px solid ${colors.primary}30`,
                borderRadius: '12px',
                padding: '20px',
                maxWidth: '400px'
              }}>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '12px',
                  marginBottom: '12px'
                }}>
                  <div style={{
                    width: '32px',
                    height: '32px',
                    background: `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)`,
                    borderRadius: '8px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <Crown size={16} color="white" />
                  </div>
                  <div>
                    <div style={{
                      color: colors.text.primary,
                      fontSize: '16px',
                      fontWeight: '600'
                    }}>
                      {profile?.plan === 'pro' ? 'Pro Plan' : profile?.plan === 'enterprise' ? 'Enterprise Plan' : 'Free Plan'}
                    </div>
                    <div style={{
                      color: colors.text.secondary,
                      fontSize: '14px'
                    }}>
                      {profile?.plan === 'free' ? 'No subscription' : profile?.subscription_status === 'active' ? '$29/month • Active' : 'Subscription inactive'}
                    </div>
                  </div>
                </div>

                <div style={{
                  display: 'flex',
                  gap: '12px',
                  marginTop: '16px'
                }}>
                  <button style={{
                    padding: '8px 16px',
                    background: colors.surface,
                    color: colors.text.primary,
                    border: `1px solid ${colors.border}`,
                    borderRadius: '6px',
                    fontSize: '14px',
                    fontWeight: '500',
                    cursor: 'pointer'
                  }}>
                    Manage Billing
                  </button>
                  <button style={{
                    padding: '8px 16px',
                    background: 'transparent',
                    color: colors.text.secondary,
                    border: `1px solid ${colors.border}`,
                    borderRadius: '6px',
                    fontSize: '14px',
                    fontWeight: '500',
                    cursor: 'pointer'
                  }}>
                    Cancel Plan
                  </button>
                </div>
              </div>
            </div>

            {/* Save Button */}
            <button
              onClick={saveProfileSettings}
              disabled={saving || !user}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                padding: '12px 24px',
                background: saving || !user ? colors.text.tertiary : `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)`,
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                fontSize: '14px',
                fontWeight: '600',
                cursor: saving || !user ? 'not-allowed' : 'pointer',
                boxShadow: saving || !user ? 'none' : `0 4px 12px ${colors.primary}30`,
                opacity: saving || !user ? 0.6 : 1
              }}
            >
              <Save size={16} />
              {saving ? 'Saving...' : 'Save Account Settings'}
            </button>
          </div>
        )}

        {/* Other tabs can be added here */}
        {activeTab !== 'agent-e' && activeTab !== 'integrations' && activeTab !== 'account' && (
          <div style={{
            textAlign: 'center',
            padding: '60px 20px',
            color: colors.text.secondary
          }}>
            <h3 style={{ marginBottom: '12px' }}>Coming Soon</h3>
            <p>This section is under development.</p>
          </div>
        )}
      </div>
    </div>
  );
};

SettingsPage.getLayout = function getLayout(page: ReactElement) {
  return (
    <SidebarLayout>
      {page}
    </SidebarLayout>
  );
};

export default SettingsPage;

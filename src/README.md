# 📈 AI Video Mentor for X (Twitter) Growth

## 🚀 Goal
Build a web application where users can have **weekly video calls** with an AI-powered mentor who helps them grow their X (Twitter) following and increase conversions/sales.

The AI mentor:
- Speaks in real-time using a realistic AI-generated voice
- Is shown as a talking face/avatar that lip-syncs to the audio
- Understands the user's past content (from X) and niche/goals
- Gives tailored advice on growth, marketing, and content writing
- Simulates a Zoom-style coaching call, but fully automated

## 🧠 AI Workflow Overview
1. User sends a message or question
2. Message sent to OpenAI GPT-4o for advice
3. GPT response converted to voice (ElevenLabs)
4. Voice synced to talking face (D-ID)
5. Video displayed in web interface
6. AI analyzes user's past tweets (X API/manual)
7. All in a clean frontend UI

## 🧩 MVP Feature Breakdown
- Frontend app (SPA):
  - User input field
  - Embedded video area
  - Area to paste X handle/tweets
  - Loading state
  - Log of last 1–3 exchanges
- Backend:
  - Route user message to GPT-4o
  - Send GPT response to ElevenLabs
  - Send audio to D-ID
  - Return video URL to frontend
  - Fetch tweets using X API (or manual)
  - Store user info temporarily

---

This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.

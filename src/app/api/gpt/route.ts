import { NextRequest, NextResponse } from 'next/server';
import OpenAI from 'openai';

const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });

export async function POST(req: NextRequest) {
  const { message, tweetsOrHandle } = await req.json();

  // Compose prompt with context
  const prompt = tweetsOrHandle
    ? `User's context: ${tweetsOrHandle}\n\nQuestion: ${message}`
    : message;

  try {
    const completion = await openai.chat.completions.create({
      model: 'gpt-4o',
      messages: [
        { role: 'system', content: 'You are an expert X (Twitter) growth coach. Give actionable, friendly, and specific advice.' },
        { role: 'user', content: prompt }
      ],
      max_tokens: 400
    });
    const reply = completion.choices[0]?.message?.content || 'Sorry, I could not generate a response.';
    return NextResponse.json({ reply });
  } catch (err) {
    return NextResponse.json({ reply: 'Error contacting OpenAI.' }, { status: 500 });
  }
} 
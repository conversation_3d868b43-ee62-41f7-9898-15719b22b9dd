'use client';

import React from 'react';
// @ts-ignore
import { createClient, createMicrophoneAndCameraTracks } from 'agora-rtc-react';

interface MentorVideoProps {
  videoUrl: string | null;
  transcript: string;
  loading: boolean;
}

const MentorVideo: React.FC<MentorVideoProps> = ({ videoUrl, transcript, loading }) => {
  return (
    <div style={{ textAlign: 'center', margin: '2rem 0' }}>
      {loading ? (
        <div className="spinner" style={{ margin: '2rem auto' }}>Loading video...</div>
      ) : videoUrl ? (
        <video src={videoUrl} controls autoPlay style={{ maxWidth: '100%', borderRadius: 12 }} />
      ) : (
        <div style={{ color: '#888' }}>Your mentor's video will appear here.</div>
      )}
      {transcript && (
        <div style={{ marginTop: 16, fontStyle: 'italic', color: '#333' }}>
          <strong>Transcript:</strong> {transcript}
        </div>
      )}
    </div>
  );
};

export default MentorVideo; 
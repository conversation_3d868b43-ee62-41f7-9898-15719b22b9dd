import Link from 'next/link';
import React, { useState } from 'react';
import { useRouter } from 'next/router';
import { Home, BarChart3, MessageCircle, Video, User, LogIn } from 'lucide-react';
import { useUser } from '../contexts/UserContext';
import AuthModal from './AuthModal';

interface SidebarLayoutProps {
  children: React.ReactNode;
}

const SidebarLayout: React.FC<SidebarLayoutProps> = ({ children }) => {
  const router = useRouter();
  const { user, profile } = useUser();
  const [showAuthModal, setShowAuthModal] = useState(false);

  // Fallback user data for when not authenticated or loading
  const userData = {
    name: profile?.full_name || user?.user_metadata?.full_name || 'Alex Chen',
    email: user?.email || '<EMAIL>',
    plan: profile?.plan === 'pro' ? 'Pro' : profile?.plan === 'enterprise' ? 'Enterprise' : 'Free',
    avatar: profile?.avatar_url || null,
    isOnline: profile?.is_online || false
  };

  const colors = {
    primary: '#FF6B35',
    primaryLight: '#FF8A65',
    background: '#F5F1EB', // Chill beige background
    surface: '#FFFFFF',
    text: {
      primary: '#2D1B14',
      secondary: '#5D4037'
    },
    sidebar: {
      background: 'linear-gradient(135deg, #FF6B35 0%, #FF8A65 100%)',
      text: '#FFFFFF'
    }
  };

  const menuItems = [
    { href: '/', label: 'Briefing Room', icon: Home },
    { href: '/tweet-center', label: 'Drafting Desk', icon: MessageCircle },
    { href: '/dashboard', label: 'Growth Lab', icon: BarChart3 },
    { href: '/meeting', label: 'AI Meetings', icon: Video }
  ];

  const isActive = (href: string) => {
    if (href === '/') {
      return router.pathname === '/';
    }
    return router.pathname.startsWith(href);
  };

  return (
    <div style={{
      display: 'flex',
      minHeight: '100vh',
      background: colors.background,
      padding: '20px',
      gap: '20px'
    }}>
      <aside style={{
        width: '160px', // More compact width
        background: colors.sidebar.background,
        minHeight: 'calc(100vh - 40px)',
        borderRadius: '12px', // Slightly smaller radius
        display: 'flex',
        flexDirection: 'column',
        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)' // Subtle shadow
      }}>
        <div style={{
          padding: '16px',
          textAlign: 'center'
        }}>
          <span style={{
            fontSize: '28px',
            color: colors.sidebar.text,
            fontFamily: 'Georgia, serif',
            fontStyle: 'italic'
          }}>
            ℰ
          </span>
        </div>

        <nav style={{ padding: '12px 12px' }}>
          {menuItems.map((item) => {
            const active = isActive(item.href);
            return (
              <div key={item.href} style={{ marginBottom: '4px' }}>
                <Link href={item.href} style={{ textDecoration: 'none' }}>
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    padding: '10px 12px',
                    borderRadius: '8px',
                    background: active ? 'rgba(255, 255, 255, 0.2)' : 'transparent',
                    cursor: 'pointer',
                    transition: 'all 0.2s ease'
                  }}
                  onMouseEnter={(e) => {
                    if (!active) {
                      e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!active) {
                      e.currentTarget.style.background = 'transparent';
                    }
                  }}
                  >
                    <item.icon size={14} color={colors.sidebar.text} />
                    <span style={{
                      marginLeft: '10px',
                      color: colors.sidebar.text,
                      fontSize: '13px',
                      fontWeight: active ? '600' : '500'
                    }}>
                      {item.label}
                    </span>
                  </div>
                </Link>
              </div>
            );
          })}
        </nav>

        {/* Spacer to push account section to bottom */}
        <div style={{ flex: 1 }}></div>

        {/* Account Section - Show different content based on auth state */}
        <div style={{
          padding: '12px',
          borderTop: '1px solid rgba(255, 255, 255, 0.3)'
        }}>
          {user ? (
            // Authenticated user - show profile
            <Link href="/settings" style={{ textDecoration: 'none' }}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '10px',
                padding: '10px',
                background: 'rgba(255, 255, 255, 0.15)',
                borderRadius: '10px',
                cursor: 'pointer',
                transition: 'all 0.2s ease',
                border: '1px solid rgba(255, 255, 255, 0.2)'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.background = 'rgba(255, 255, 255, 0.25)';
                e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.4)';
                e.currentTarget.style.transform = 'translateY(-1px)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.background = 'rgba(255, 255, 255, 0.15)';
                e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.2)';
                e.currentTarget.style.transform = 'translateY(0)';
              }}
              >
                <div style={{
                  width: '28px',
                  height: '28px',
                  background: 'rgba(255, 255, 255, 0.3)',
                  borderRadius: '8px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  position: 'relative'
                }}>
                  <User size={14} color={colors.sidebar.text} />
                  {/* Online status dot - only show if user is online */}
                  {userData.isOnline && (
                    <div style={{
                      position: 'absolute',
                      bottom: '-2px',
                      right: '-2px',
                      width: '8px',
                      height: '8px',
                      background: '#00FF88',
                      borderRadius: '50%',
                      border: '2px solid rgba(255, 255, 255, 0.9)',
                      boxShadow: '0 0 4px rgba(0, 255, 136, 0.6)'
                    }} />
                  )}
                </div>
                <div style={{ flex: 1 }}>
                  <div style={{
                    color: colors.sidebar.text,
                    fontSize: '13px',
                    fontWeight: '600',
                    lineHeight: '1.2'
                  }}>
                    {userData.name}
                  </div>
                  <div style={{
                    color: 'rgba(255, 255, 255, 0.8)',
                    fontSize: '11px',
                    fontWeight: '400',
                    lineHeight: '1.2',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '4px'
                  }}>
                    <span>{userData.plan} Plan</span>
                    <span>•</span>
                    <span>Settings</span>
                  </div>
                </div>
              </div>
            </Link>
          ) : (
            // Not authenticated - show sign in button
            <div
              onClick={() => setShowAuthModal(true)}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '10px',
                padding: '10px',
                background: 'rgba(255, 255, 255, 0.15)',
                borderRadius: '10px',
                cursor: 'pointer',
                transition: 'all 0.2s ease',
                border: '1px solid rgba(255, 255, 255, 0.2)'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.background = 'rgba(255, 255, 255, 0.25)';
                e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.4)';
                e.currentTarget.style.transform = 'translateY(-1px)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.background = 'rgba(255, 255, 255, 0.15)';
                e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.2)';
                e.currentTarget.style.transform = 'translateY(0)';
              }}
            >
              <div style={{
                width: '28px',
                height: '28px',
                background: 'rgba(255, 255, 255, 0.3)',
                borderRadius: '8px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <LogIn size={14} color={colors.sidebar.text} />
              </div>
              <div style={{ flex: 1 }}>
                <div style={{
                  color: colors.sidebar.text,
                  fontSize: '13px',
                  fontWeight: '600',
                  lineHeight: '1.2'
                }}>
                  Sign In
                </div>
                <div style={{
                  color: 'rgba(255, 255, 255, 0.8)',
                  fontSize: '11px',
                  fontWeight: '400',
                  lineHeight: '1.2'
                }}>
                  Access your account
                </div>
              </div>
            </div>
          )}
        </div>
      </aside>

      <main style={{
        flexGrow: 1,
        backgroundColor: colors.surface,
        borderRadius: '20px',
        minHeight: 'calc(100vh - 40px)',
        position: 'relative'
      }}>
        <div style={{
          padding: '40px',
          height: '100%'
        }}>
          {children}
        </div>
      </main>

      {/* Authentication Modal */}
      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
      />
    </div>
  );
};

export default SidebarLayout;

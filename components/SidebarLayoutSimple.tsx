import Link from 'next/link';
import React, { useState } from 'react';
import { useRouter } from 'next/router';
import { Home, BarChart3, MessageCircle, Video, User, Settings, Crown } from 'lucide-react';

interface SidebarLayoutProps {
  children: React.ReactNode;
}

const SidebarLayout: React.FC<SidebarLayoutProps> = ({ children }) => {
  const router = useRouter();
  const [showUserMenu, setShowUserMenu] = useState(false);

  // User data - in a real app, this would come from context/state management
  const userData = {
    name: '<PERSON>',
    email: '<EMAIL>',
    plan: 'Pro',
    avatar: null // Could be an image URL
  };

  const colors = {
    primary: '#FF6B35',
    primaryLight: '#FF8A65',
    background: '#F5F1EB', // Chill beige background
    surface: '#FFFFFF',
    text: {
      primary: '#2D1B14',
      secondary: '#5D4037'
    },
    sidebar: {
      background: 'linear-gradient(135deg, #FF6B35 0%, #FF8A65 100%)',
      text: '#FFFFFF'
    }
  };

  const menuItems = [
    { href: '/', label: 'Briefing Room', icon: Home },
    { href: '/tweet-center', label: 'Drafting Desk', icon: MessageCircle },
    { href: '/dashboard', label: 'Growth Lab', icon: BarChart3 },
    { href: '/meeting', label: 'AI Meetings', icon: Video }
  ];

  const isActive = (href: string) => {
    if (href === '/') {
      return router.pathname === '/';
    }
    return router.pathname.startsWith(href);
  };

  return (
    <div style={{
      display: 'flex',
      minHeight: '100vh',
      background: colors.background,
      padding: '20px',
      gap: '20px'
    }}>
      <aside style={{
        width: '160px', // More compact width
        background: colors.sidebar.background,
        minHeight: 'calc(100vh - 40px)',
        borderRadius: '12px', // Slightly smaller radius
        display: 'flex',
        flexDirection: 'column',
        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)' // Subtle shadow
      }}>
        <div style={{
          padding: '16px',
          textAlign: 'center'
        }}>
          <span style={{
            fontSize: '28px',
            color: colors.sidebar.text,
            fontFamily: 'Georgia, serif',
            fontStyle: 'italic'
          }}>
            ℰ
          </span>
        </div>

        <nav style={{ padding: '12px 12px' }}>
          {menuItems.map((item) => {
            const active = isActive(item.href);
            return (
              <div key={item.href} style={{ marginBottom: '4px' }}>
                <Link href={item.href} style={{ textDecoration: 'none' }}>
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    padding: '10px 12px',
                    borderRadius: '8px',
                    background: active ? 'rgba(255, 255, 255, 0.2)' : 'transparent',
                    cursor: 'pointer',
                    transition: 'all 0.2s ease'
                  }}
                  onMouseEnter={(e) => {
                    if (!active) {
                      e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!active) {
                      e.currentTarget.style.background = 'transparent';
                    }
                  }}
                  >
                    <item.icon size={14} color={colors.sidebar.text} />
                    <span style={{
                      marginLeft: '10px',
                      color: colors.sidebar.text,
                      fontSize: '13px',
                      fontWeight: active ? '600' : '500'
                    }}>
                      {item.label}
                    </span>
                  </div>
                </Link>
              </div>
            );
          })}
        </nav>

        {/* Spacer to push account section to bottom */}
        <div style={{ flex: 1 }}></div>

        {/* Enhanced Account Section */}
        <div style={{
          padding: '12px',
          borderTop: '1px solid rgba(255, 255, 255, 0.3)'
        }}>
          <Link href="/settings" style={{ textDecoration: 'none' }}>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              gap: '10px',
              padding: '12px',
              background: 'rgba(255, 255, 255, 0.15)',
              borderRadius: '12px',
              cursor: 'pointer',
              transition: 'all 0.2s ease',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              position: 'relative'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.background = 'rgba(255, 255, 255, 0.25)';
              e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.4)';
              e.currentTarget.style.transform = 'translateY(-1px)';
              e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = 'rgba(255, 255, 255, 0.15)';
              e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.2)';
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = 'none';
            }}
            >
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '12px'
              }}>
                <div style={{
                  width: '32px',
                  height: '32px',
                  background: 'rgba(255, 255, 255, 0.3)',
                  borderRadius: '10px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  position: 'relative'
                }}>
                  <User size={16} color={colors.sidebar.text} />
                  {/* Pro badge */}
                  <div style={{
                    position: 'absolute',
                    top: '-4px',
                    right: '-4px',
                    width: '12px',
                    height: '12px',
                    background: '#FFD700',
                    borderRadius: '50%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    border: '2px solid rgba(255, 255, 255, 0.9)'
                  }}>
                    <Crown size={6} color="#FF6B35" />
                  </div>
                </div>
                <div>
                  <div style={{
                    color: colors.sidebar.text,
                    fontSize: '13px',
                    fontWeight: '600',
                    lineHeight: '1.2',
                    marginBottom: '2px'
                  }}>
                    {userData.name}
                  </div>
                  <div style={{
                    color: 'rgba(255, 255, 255, 0.8)',
                    fontSize: '10px',
                    fontWeight: '500',
                    lineHeight: '1.2',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '4px'
                  }}>
                    <span>{userData.plan} Plan</span>
                    <span>•</span>
                    <span>Settings</span>
                  </div>
                </div>
              </div>
              <div style={{
                color: 'rgba(255, 255, 255, 0.7)',
                fontSize: '12px',
                fontWeight: '600'
              }}>
                <Settings size={14} />
              </div>
            </div>
          </Link>
        </div>
      </aside>

      <main style={{
        flexGrow: 1,
        backgroundColor: colors.surface,
        borderRadius: '20px',
        minHeight: 'calc(100vh - 40px)',
        position: 'relative'
      }}>
        <div style={{
          padding: '40px',
          height: '100%'
        }}>
          {children}
        </div>
      </main>
    </div>
  );
};

export default SidebarLayout;

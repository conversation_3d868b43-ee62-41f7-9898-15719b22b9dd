{"name": "exie-ai", "version": "0.1.0", "private": true, "dependencies": {"@daily-co/daily-js": "^0.79.0", "@daily-co/daily-react": "^0.22.0", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/auth-helpers-react": "^0.5.0", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/supabase-js": "^2.49.8", "autoprefixer": "^10.4.0", "axios": "^1.9.0", "clsx": "^2.0.0", "dotenv": "^16.5.0", "express": "^5.1.0", "geist": "^1.0.0", "jotai": "^2.0.0", "lucide-react": "^0.400.0", "next": "^14.0.0", "openai": "^4.103.0", "postcss": "^8.4.0", "react": "^18.0.0", "react-dom": "^18.0.0", "react-player": "^2.16.0", "tailwindcss": "^3.4.0"}, "devDependencies": {"@types/node": "^20.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "typescript": "5.8.3"}, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}}